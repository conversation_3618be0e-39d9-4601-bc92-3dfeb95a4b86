@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Mode - Clean, modern light theme */
    --background: 0 0% 100%; /* Pure white background */
    --foreground: 240 10% 3.9%; /* Very dark text */

    --card: 0 0% 100%; /* White cards */
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 158 64% 52%; /* Emerald green primary */
    --primary-foreground: 0 0% 100%; /* White text on primary */

    --secondary: 240 4.8% 95.9%; /* Light gray secondary */
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%; /* Light muted background */
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%; /* Light accent */
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%; /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%; /* Light border */
    --input: 240 5.9% 90%; /* Light input background */
    --ring: 158 64% 52%; /* Emerald ring color */

    /* Chart colors - emerald/purple theme */
    --chart-1: 158 64% 52%; /* Emerald */
    --chart-2: 262 83% 58%; /* Purple */
    --chart-3: 198 93% 60%; /* Cyan */
    --chart-4: 280 100% 70%; /* Magenta */
    --chart-5: 47 96% 53%; /* Yellow */

    --radius: 0.75rem;

    /* Glass-morphism variables */
    --glass-background: 255 255 255 / 0.1;
    --glass-border: 255 255 255 / 0.2;
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Sidebar for light mode */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 158 64% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
    --sidebar-ring: 158 64% 52%;
  }

  .dark {
    /* Dark Mode - Purple gradient theme matching homepage */
    --background: 262 83% 8%; /* Deep purple background */
    --foreground: 0 0% 98%; /* White text */

    --card: 262 50% 12%; /* Dark purple cards with transparency */
    --card-foreground: 0 0% 98%;

    --popover: 262 50% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 158 64% 52%; /* Emerald green primary (consistent) */
    --primary-foreground: 0 0% 100%;

    --secondary: 262 30% 18%; /* Dark purple secondary */
    --secondary-foreground: 0 0% 98%;

    --muted: 262 30% 15%; /* Muted dark purple */
    --muted-foreground: 240 5% 64.9%;

    --accent: 262 30% 18%; /* Dark purple accent */
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%; /* Dark red */
    --destructive-foreground: 0 0% 98%;

    --border: 262 30% 18%; /* Dark purple border */
    --input: 262 30% 15%; /* Dark input background */
    --ring: 158 64% 52%; /* Emerald ring */

    /* Chart colors for dark mode */
    --chart-1: 158 64% 52%; /* Emerald */
    --chart-2: 262 83% 58%; /* Purple */
    --chart-3: 198 93% 60%; /* Cyan */
    --chart-4: 280 100% 70%; /* Magenta */
    --chart-5: 47 96% 53%; /* Yellow */

    /* Glass-morphism for dark mode */
    --glass-background: 255 255 255 / 0.05;
    --glass-border: 255 255 255 / 0.1;
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);

    /* Dark sidebar with glass effect */
    --sidebar-background: 262 50% 8%; /* Very dark purple */
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 158 64% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 262 30% 15%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 262 30% 18%;
    --sidebar-ring: 158 64% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Glass-morphism utilities */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-card {
    @apply bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl;
  }

  .glass-strong {
    @apply bg-white/20 backdrop-blur-lg border border-white/30;
  }

  /* Dark mode glass effects */
  .dark .glass {
    @apply bg-white/5 border-white/10;
  }

  .dark .glass-card {
    @apply bg-white/5 border-white/10;
  }

  .dark .glass-strong {
    @apply bg-white/10 border-white/20;
  }

  /* Gradient backgrounds */
  .bg-gradient-purple {
    @apply bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900;
  }

  .bg-gradient-emerald {
    @apply bg-gradient-to-r from-emerald-500 to-emerald-600;
  }

  /* Mobile-first responsive utilities */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8;
  }

  .text-responsive {
    @apply text-sm sm:text-base;
  }

  .heading-responsive {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
  }

  .card-responsive {
    @apply p-3 sm:p-4 md:p-6;
  }

  .button-responsive {
    @apply px-3 py-2 sm:px-4 sm:py-2.5 text-sm sm:text-base;
  }

  .grid-responsive {
    @apply grid gap-3 sm:gap-4 md:gap-6 lg:gap-8;
  }

  .space-responsive {
    @apply space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8;
  }

  /* Mobile table improvements */
  .table-mobile {
    @apply text-xs sm:text-sm;
  }

  .table-mobile th {
    @apply px-2 py-2 sm:px-4 sm:py-3;
  }

  .table-mobile td {
    @apply px-2 py-2 sm:px-4 sm:py-3;
  }

  /* Mobile form improvements */
  .form-mobile input,
  .form-mobile textarea,
  .form-mobile select {
    @apply text-sm sm:text-base;
  }

  /* Mobile sidebar improvements */
  .sidebar-mobile {
    @apply w-64 sm:w-72;
  }
}

/* Remove sidebar gap - Fix layout spacing */
[data-sidebar="sidebar"] + * {
  margin-left: 0 !important;
}

/* Remove any default spacing from SidebarInset */
[data-sidebar="inset"] {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Ensure no gap between sidebar and content */
.sidebar-provider {
  gap: 0 !important;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}
