@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Mode - Modern slate/blue theme */
    --background: 210 20% 98%; /* Light slate background */
    --foreground: 215 25% 15%; /* Dark slate text */

    --card: 0 0% 100%; /* Pure white cards */
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 217 91% 60%; /* Bright blue primary */
    --primary-foreground: 0 0% 100%; /* White text on primary */

    --secondary: 210 40% 95%; /* Light blue-gray secondary */
    --secondary-foreground: 215 25% 25%;

    --muted: 210 40% 96%; /* Very light blue-gray muted */
    --muted-foreground: 215 16% 46%;

    --accent: 212 95% 95%; /* Light blue accent */
    --accent-foreground: 217 91% 60%;

    --destructive: 0 84.2% 60.2%; /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%; /* Light border */
    --input: 240 5.9% 90%; /* Light input background */
    --ring: 158 64% 52%; /* Emerald ring color */

    /* Chart colors - emerald/purple theme */
    --chart-1: 158 64% 52%; /* Emerald */
    --chart-2: 262 83% 58%; /* Purple */
    --chart-3: 198 93% 60%; /* Cyan */
    --chart-4: 280 100% 70%; /* Magenta */
    --chart-5: 47 96% 53%; /* Yellow */

    --radius: 0.75rem;

    /* Glass-morphism variables */
    --glass-background: 255 255 255 / 0.1;
    --glass-border: 255 255 255 / 0.2;
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Sidebar for light mode */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 158 64% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
    --sidebar-ring: 158 64% 52%;
  }

  .dark {
    /* Dark Mode - Modern dark slate theme */
    --background: 215 28% 8%; /* Deep dark slate */
    --foreground: 210 20% 95%; /* Light text */

    --card: 215 25% 12%; /* Dark slate cards */
    --card-foreground: 210 20% 95%;

    --popover: 215 25% 12%;
    --popover-foreground: 210 20% 95%;

    --primary: 217 91% 60%; /* Bright blue primary (consistent) */
    --primary-foreground: 0 0% 100%;

    --secondary: 215 25% 18%; /* Dark slate secondary */
    --secondary-foreground: 210 20% 90%;

    --muted: 215 25% 15%; /* Muted dark slate */
    --muted-foreground: 215 16% 65%;

    --accent: 215 25% 18%; /* Dark slate accent */
    --accent-foreground: 217 91% 60%;

    --destructive: 0 62.8% 30.6%; /* Dark red */
    --destructive-foreground: 0 0% 98%;

    --border: 262 30% 18%; /* Dark purple border */
    --input: 262 30% 15%; /* Dark input background */
    --ring: 158 64% 52%; /* Emerald ring */

    /* Chart colors for dark mode */
    --chart-1: 158 64% 52%; /* Emerald */
    --chart-2: 262 83% 58%; /* Purple */
    --chart-3: 198 93% 60%; /* Cyan */
    --chart-4: 280 100% 70%; /* Magenta */
    --chart-5: 47 96% 53%; /* Yellow */

    /* Glass-morphism for dark mode */
    --glass-background: 255 255 255 / 0.05;
    --glass-border: 255 255 255 / 0.1;
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);

    /* Dark sidebar with glass effect */
    --sidebar-background: 262 50% 8%; /* Very dark purple */
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 158 64% 52%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 262 30% 15%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 262 30% 18%;
    --sidebar-ring: 158 64% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Glass-morphism utilities */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-card {
    @apply bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl;
  }

  .glass-strong {
    @apply bg-white/20 backdrop-blur-lg border border-white/30;
  }

  /* Dark mode glass effects */
  .dark .glass {
    @apply bg-white/5 border-white/10;
  }

  .dark .glass-card {
    @apply bg-white/5 border-white/10;
  }

  .dark .glass-strong {
    @apply bg-white/10 border-white/20;
  }

  /* Modern gradient backgrounds */
  .bg-gradient-slate {
    @apply bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900;
  }

  .bg-gradient-blue {
    @apply bg-gradient-to-r from-blue-600 to-blue-500;
  }

  .bg-gradient-light {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50;
  }

  /* New modern card styles */
  .modern-card {
    @apply bg-white/80 backdrop-blur-sm border border-slate-200/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .dark .modern-card {
    @apply bg-slate-800/80 border-slate-700/50 shadow-slate-900/20;
  }

  /* Enhanced glass effects */
  .glass-modern {
    @apply bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl;
  }

  .dark .glass-modern {
    @apply bg-slate-800/30 border-slate-700/30;
  }

  /* Mobile-first responsive utilities */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8;
  }

  .text-responsive {
    @apply text-sm sm:text-base;
  }

  .heading-responsive {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
  }

  .card-responsive {
    @apply p-3 sm:p-4 md:p-6;
  }

  .button-responsive {
    @apply px-3 py-2 sm:px-4 sm:py-2.5 text-sm sm:text-base;
  }

  .grid-responsive {
    @apply grid gap-3 sm:gap-4 md:gap-6 lg:gap-8;
  }

  .space-responsive {
    @apply space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8;
  }

  /* Mobile table improvements */
  .table-mobile {
    @apply text-xs sm:text-sm;
  }

  .table-mobile th {
    @apply px-2 py-2 sm:px-4 sm:py-3;
  }

  .table-mobile td {
    @apply px-2 py-2 sm:px-4 sm:py-3;
  }

  /* Mobile form improvements */
  .form-mobile input,
  .form-mobile textarea,
  .form-mobile select {
    @apply text-sm sm:text-base;
  }

  /* Mobile sidebar improvements */
  .sidebar-mobile {
    @apply w-64 sm:w-72;
  }
}

/* Remove sidebar gap - Fix layout spacing */
[data-sidebar="sidebar"] + * {
  margin-left: 0 !important;
}

/* Remove any default spacing from SidebarInset */
[data-sidebar="inset"] {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Ensure no gap between sidebar and content */
.sidebar-provider {
  gap: 0 !important;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}
