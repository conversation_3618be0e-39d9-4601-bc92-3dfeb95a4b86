
// src/app/dashboard/page.tsx
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowRight, FileText, PlusCircle, BarChart, Brain, Clock, AlertCircle, DatabaseZap, Wand2, BarChartBig, Save } from "lucide-react";
import Link from "next/link";
import { getUserActivities, getUserDatasets, type ActivityLog, type SavedDataset } from '@/lib/supabase/actions';
import { format, formatDistanceToNow } from 'date-fns';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

const quickActions = [
  { title: "Generate New Dataset", Icon: PlusCircle, href: "/dashboard/generate", description: "Start generating a new synthetic dataset.", cta: "Generate Data" },
  { title: "View Activity History", Icon: Clock, href: "/dashboard/history", description: "Review your past activities and logs.", cta: "View History"},
  { title: "Train New Model", Icon: Brain, href: "/dashboard/train", description: "Train a machine learning model with your data.", cta: "Train Model" },
  { title: "Analyze Dataset", Icon: BarChart, href: "/dashboard/analysis", description: "Explore insights from your datasets with AI.", cta: "Analyze Data"},
];

function getActivityIcon(activityType: string) {
  switch (activityType) {
    case "DATA_GENERATION": return <DatabaseZap className="h-5 w-5 text-emerald-400" />;
    case "PROMPT_ENHANCEMENT": return <Wand2 className="h-5 w-5 text-purple-400" />;
    case "DATA_ANALYSIS_SNIPPET": return <BarChartBig className="h-5 w-5 text-cyan-400" />;
    case "DATASET_SAVED": return <Save className="h-5 w-5 text-emerald-400" />;
    default: return <AlertCircle className="h-5 w-5 text-white/60" />;
  }
}

export default async function DashboardPage() {
  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  const { data: { user } } = await supabase.auth.getUser();
  const userName = user?.user_metadata?.full_name || user?.email?.split('@')[0] || "User";
  
  // Fetch activities and datasets only if user is available
  // To prevent errors if middleware hasn't fully processed or if user becomes null
  let recentActivities: ActivityLog[] = [];
  let lastSavedDataset: (SavedDataset & { data_csv?: string }) | null = null; // Ensure type matches

  if (user) {
    recentActivities = await getUserActivities(5); 
    const savedDatasets = await getUserDatasets(1); 
    lastSavedDataset = savedDatasets.length > 0 ? savedDatasets[0] : null;
  }


  return (
    <div className="space-y-6 sm:space-y-8 lg:space-y-10 xl:space-y-12">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-headline font-bold text-white">Welcome Back, {userName}!</h1>
          <p className="text-sm sm:text-base lg:text-lg text-white/70 mt-1">Here's what's happening with your Synthara projects.</p>
        </div>
        <Button size="lg" asChild className="bg-emerald-500 hover:bg-emerald-600 text-white shadow-xl hover:shadow-2xl transition-all w-full sm:w-auto">
          <Link href="/dashboard/generate">
            <PlusCircle className="mr-2 h-4 w-4 sm:h-5 sm:w-5" /> New Dataset
          </Link>
        </Button>
      </div>

      {/* Quick Action Cards */}
      <section>
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-headline font-semibold mb-4 sm:mb-6 text-white">Quick Actions</h2>
        <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => (
            <div key={action.title} className="glass-card hover:glass-strong transition-all duration-300 ease-in-out transform hover:-translate-y-1 hover:scale-105 flex flex-col p-6">
              <div className="pb-3">
                <div className="p-3 bg-emerald-500/20 rounded-lg inline-block mb-3 self-start">
                    <action.Icon className="h-6 w-6 lg:h-7 lg:w-7 text-emerald-400" />
                </div>
                <h3 className="text-lg sm:text-xl font-headline font-semibold text-white">{action.title}</h3>
              </div>
              <div className="flex-grow">
                <p className="text-xs sm:text-sm text-white/70 leading-relaxed mb-4">{action.description}</p>
              </div>
              <div>
                <Button variant="default" className="w-full group text-sm bg-emerald-500 hover:bg-emerald-600 text-white" asChild>
                  <Link href={action.href}>
                    {action.cta} <ArrowRight className="ml-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:translate-x-1 transition-transform"/>
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </section>

      <div className="grid gap-6 sm:gap-8 grid-cols-1 lg:grid-cols-3">
        {/* Recent Activity Feed */}
        <section className="lg:col-span-2">
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-headline font-semibold mb-4 sm:mb-6 text-white">Recent Activity</h2>
          <div className="glass-card p-6">
            <div className="mb-6">
                <h3 className="text-xl font-headline font-semibold text-white">Activity Feed</h3>
                <p className="text-white/70 text-sm">Your latest actions and events.</p>
            </div>
            <div>
              {recentActivities && recentActivities.length > 0 ? (
                <ul className="divide-y divide-white/10">
                  {recentActivities.map(activity => (
                    <li key={activity.id} className="p-4 hover:bg-white/5 transition-colors rounded-lg">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-white/10 rounded-full mt-0.5">
                           {getActivityIcon(activity.activity_type)}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-white">{activity.description}</p>
                          <p className="text-xs text-white/60">
                            {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                          </p>
                        </div>
                         {activity.related_resource_id && activity.activity_type === 'DATASET_SAVED' && (
                            <Button variant="outline" size="sm" className="ml-auto border-white/20 text-white hover:bg-white/10" asChild>
                                <Link href={`/dashboard/preview?datasetId=${activity.related_resource_id}`}>View Dataset</Link>
                            </Button>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-center text-white/60 p-6 min-h-[200px] flex flex-col items-center justify-center">
                  <Clock className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p className="font-medium text-white">No recent activity to display.</p>
                  <p className="text-sm">Start using the platform to see your activity log populate.</p>
                </div>
              )}
            </div>
             <div className="py-4 border-t border-white/10 mt-6">
                <Button variant="outline" size="sm" className="ml-auto border-white/20 text-white hover:bg-white/10" asChild>
                   <Link href="/dashboard/history">View All Activity</Link>
                </Button>
            </div>
          </div>
        </section>

        {/* Last Generated Dataset */}
        <section>
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-headline font-semibold mb-4 sm:mb-6 text-white">Last Dataset</h2>
          <div className="glass-card hover:glass-strong transition-all flex flex-col h-full p-6">
            <div className="mb-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2.5 bg-emerald-500/20 rounded-lg">
                    <FileText className="h-7 w-7 text-emerald-400" />
                </div>
                <h3 className="text-xl font-headline font-semibold text-white truncate">
                  {lastSavedDataset ? lastSavedDataset.dataset_name : "No Dataset Saved Yet"}
                </h3>
              </div>
              <p className="text-white/70 text-sm">
                {lastSavedDataset ? `Saved ${formatDistanceToNow(new Date(lastSavedDataset.created_at), { addSuffix: true })}` : "Details of your last saved dataset will show here."}
              </p>
            </div>
            <div className="space-y-4 flex-grow">
              <div className="text-sm text-white/80 space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/60">Rows:</span>
                  <span className="font-medium">{lastSavedDataset ? lastSavedDataset.num_rows.toLocaleString() : "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Columns:</span>
                  <span className="font-medium">{lastSavedDataset && Array.isArray(lastSavedDataset.schema_json) ? lastSavedDataset.schema_json.length : "N/A"}</span>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-white/60">Prompt:</span>
                  <span className="font-medium text-xs bg-white/10 p-2 rounded">{lastSavedDataset ? `${lastSavedDataset.prompt_used.substring(0, 80)}...` : "N/A"}</span>
                </div>
              </div>
              <Progress value={lastSavedDataset ? 100 : 0} aria-label="Dataset information available" className="h-2.5" />
            </div>
            <div className="border-t border-white/10 pt-4 mt-6">
              <Button className="w-full bg-emerald-500 hover:bg-emerald-600 text-white" disabled={!lastSavedDataset} asChild>
                <Link href={lastSavedDataset ? `/dashboard/preview?datasetId=${lastSavedDataset.id}` : "#"}>View Dataset Details</Link>
              </Button>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
